# -*- coding: utf-8 -*-
"""
文件视图 - 中间文件显示区域组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QScrollArea, QLabel, QPushButton, QFileDialog,
                            QMessageBox, QMenu, QApplication, QRubberBand,
                            QInputDialog, QFrame, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal, QPoint, QRect, QSize, QMimeData
from PyQt6.QtGui import (QPixmap, QFont, QDragEnterEvent, QDropEvent, QContextMenuEvent,
                        QMouseEvent, QPainter, QPen, QColor, QPaintEvent)

from models.filesystem_manager import FileSystemManager
from utils.config_manager import ConfigManager
from utils.file_operations import FileOperations
from utils.thumbnail import ThumbnailGenerator
from utils.drag_drop import DragDropManager
from ui.rename_dialog import RenameDialog
from ui.preview_dialog import PreviewDialog
from ui.batch_rename_dialog import BatchRenameDialog
from ui.color_dialog import ColorDialog
import os
import time
from pathlib import Path

class BreadcrumbBar(QWidget):
    """面包屑导航栏，支持拖放"""
    path_selected = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self._layout = QHBoxLayout(self)
        self._layout.setContentsMargins(0, 0, 0, 0)
        self._layout.setSpacing(5)
        self.setAcceptDrops(True)

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event is None:
            return
        mime_data = event.mimeData()
        if mime_data and (mime_data.hasUrls() or mime_data.hasFormat("application/x-file-info")):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        if event is None:
            return
        target_widget = self.childAt(event.position().toPoint())
        if isinstance(target_widget, QPushButton):
            path = target_widget.property("path")
            self.path_selected.emit(path)
        event.accept()

# 导入系统回收站功能
try:
    from send2trash import send2trash
    SEND2TRASH_AVAILABLE = True
except ImportError:
    SEND2TRASH_AVAILABLE = False
    print("警告: send2trash 库未安装，将使用自定义回收站功能")


class FileItemWidget(QWidget):
    """文件项组件"""

    # 信号
    clicked = pyqtSignal(dict)  # 点击信号
    double_clicked = pyqtSignal(dict)  # 双击信号

    def __init__(self, file_info: dict, thumbnail_generator: ThumbnailGenerator,
                 drag_drop_manager: DragDropManager, thumbnail_size: int = 100):
        super().__init__()
        self.file_info = file_info
        self.thumbnail_generator = thumbnail_generator
        self.drag_drop_manager = drag_drop_manager
        self.thumbnail_size = thumbnail_size
        self.selected = False
        self.drag_start_position = QPoint()

        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)



        # 缩略图
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(self.thumbnail_size, self.thumbnail_size)
        self.thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #4a4a4a;
            }
        """)

        # 加载缩略图
        self.load_thumbnail()
        layout.addWidget(self.thumbnail_label)

        # 文件名
        self.name_label = QLabel(self.file_info.get('name', '未知'))
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name_label.setWordWrap(False) # 禁用自动换行
        self.name_label.setTextInteractionFlags(Qt.TextInteractionFlag.NoTextInteraction)
        self.name_label.setToolTip(self.file_info.get('name', '未知')) # 添加悬浮提示
        
        self.name_label.setMaximumWidth(self.thumbnail_size + 10)
        self.name_label.setFont(QFont("Microsoft YaHei", 9))
        layout.addWidget(self.name_label)

        # 设置固定大小
        self.setFixedSize(self.thumbnail_size + 20, self.thumbnail_size + 50)

    def load_thumbnail(self):
        """加载缩略图"""
        file_path = self.file_info.get('path', '')

        if file_path and os.path.exists(file_path):
            # 生成缩略图
            thumbnail_path = self.thumbnail_generator.generate_thumbnail(file_path)

            if thumbnail_path and os.path.exists(thumbnail_path):
                # 加载缩略图
                pixmap = QPixmap(thumbnail_path)
                if not pixmap.isNull():
                    # 缩放到合适大小
                    scaled_pixmap = pixmap.scaled(
                        self.thumbnail_size - 4, self.thumbnail_size - 4,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.thumbnail_label.setPixmap(scaled_pixmap)
                    return

        # 如果无法加载缩略图，显示默认图标
        self._show_default_icon()

    def _show_default_icon(self):
        """显示默认图标"""
        file_type = self.file_info.get('type', '').lower()

        if file_type in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
            icon = "🖼️"
        elif file_type in ['mp4', 'avi', 'mov', 'mkv', 'wmv']:
            icon = "🎬"
        elif file_type in ['mp3', 'wav', 'flac', 'aac', 'ogg']:
            icon = "🎵"
        elif file_type in ['txt', 'md', 'py', 'js', 'html', 'css']:
            icon = "📄"
        else:
            icon = "📁" if self.file_info.get('is_folder', False) else "📄"

        self.thumbnail_label.setText(icon)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                border: 2px solid #5a5a5a;
                border-radius: 5px;
                background-color: #4a4a4a;
                font-size: 32px;
                color: #ffffff;
            }
        """)
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.position().toPoint()
            self.clicked.emit(self.file_info)

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 处理拖拽"""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return

        if ((event.position().toPoint() - self.drag_start_position).manhattanLength() <
            QApplication.startDragDistance()):
            return

        # 开始拖拽
        self.drag_drop_manager.start_drag(self, self.file_info)

    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.double_clicked.emit(self.file_info)
    
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        if selected:
            # 选中状态：蓝色边框和半透明背景
            self.setStyleSheet("""
                QWidget {
                    background-color: rgba(58, 123, 213, 0.2);
                    border: 2px solid #3a7bd5;
                    border-radius: 8px;
                }
            """)
            # 更新缩略图边框
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    border: 3px solid #3a7bd5;
                    border-radius: 5px;
                    background-color: #4a4a4a;
                    font-size: 32px;
                }
            """)
            # 更新文件名样式
            self.name_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-weight: bold;
                }
            """)
        else:
            # 未选中状态：恢复默认样式
            self.setStyleSheet("""
                QWidget {
                    background-color: transparent;
                    border: none;
                    border-radius: 5px;
                }
                QWidget:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                    border: 1px solid #666;
                    border-radius: 5px;
                }
            """)
            # 恢复缩略图默认边框
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #5a5a5a;
                    border-radius: 5px;
                    background-color: #4a4a4a;
                    font-size: 32px;
                }
            """)
            # 恢复文件名默认样式
            self.name_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-weight: normal;
                }
            """)




class FileView(QWidget):
    """文件视图组件"""

    # 信号
    file_selected = pyqtSignal(dict)  # 文件选中信号
    statusbar_message = pyqtSignal(str)  # 状态栏消息信号
    folder_entered = pyqtSignal(str, str)  # 文件夹进入信号：分类，子文件夹路径
    category_changed = pyqtSignal(str, str) # 分类切换信号
    
    def __init__(self, config_manager: ConfigManager, fs_manager: FileSystemManager):
        super().__init__()
        self.config_manager = config_manager
        self.fs_manager = fs_manager
        self.file_operations = FileOperations(config_manager, fs_manager)
        self.thumbnail_generator = ThumbnailGenerator(config_manager)
        self.drag_drop_manager = DragDropManager(config_manager, fs_manager)

        self.current_category = ""
        self.current_path = ""
        self.current_subfolder = ""  # 当前子文件夹
        self.file_widgets = []
        self.selected_files = []


        # 框选功能相关变量
        self.rubber_band = None
        self.selection_start_point = QPoint()
        self.is_selecting = False

        self.init_ui()
        self.setup_drag_drop()
        self.connect_drag_drop_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 面包屑导航栏
        nav_layout = QHBoxLayout()

        # 导航容器
        self.nav_container = BreadcrumbBar()
        self.nav_container.path_selected.connect(self.navigate_to_path)
        self.nav_layout = QHBoxLayout(self.nav_container)
        self.nav_layout.setContentsMargins(0, 0, 0, 0)
        self.nav_layout.setSpacing(5)
        nav_layout.addWidget(self.nav_container)

        nav_layout.addStretch()
        
        layout.addLayout(nav_layout)
        
        # 文件显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 文件容器
        self.file_container = QWidget()
        self.file_layout = QGridLayout(self.file_container)
        self.file_layout.setSpacing(10)
        
        self.scroll_area.setWidget(self.file_container)
        layout.addWidget(self.scroll_area)
        
        # 底部工具栏
        bottom_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all)
        bottom_layout.addWidget(self.select_all_btn)
        
        self.rename_btn = QPushButton("批量重命名")
        self.rename_btn.clicked.connect(self.batch_rename)
        bottom_layout.addWidget(self.rename_btn)
        
        self.delete_btn = QPushButton("删除选中")
        self.delete_btn.clicked.connect(self.delete_selected)
        bottom_layout.addWidget(self.delete_btn)
        

        
        bottom_layout.addStretch()
        
        layout.addLayout(bottom_layout)
        
        # 应用样式
        self.apply_styles()
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            padding: 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px 10px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QScrollArea {
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            background-color: #3c3c3c;
        }
        """
        self.setStyleSheet(style)
    
    def setup_drag_drop(self):
        """设置拖放功能"""
        self.setAcceptDrops(True)

    def connect_drag_drop_signals(self):
        """连接拖放信号"""
        self.drag_drop_manager.files_dropped.connect(self.on_files_dropped)
        self.drag_drop_manager.file_moved.connect(self.on_file_moved)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        mime_data = event.mimeData()
        if mime_data and self.drag_drop_manager.can_accept_drop(mime_data, self.current_category):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖放事件"""
        mime_data = event.mimeData()
        if not mime_data:
            return

        # 检查拖放源
        source_widget = event.source()
        is_internal_drag = isinstance(source_widget, QWidget) and source_widget.window() is self.window()

        # 如果是内部拖拽，强制使用移动操作
        if is_internal_drag:
            event.setDropAction(Qt.DropAction.MoveAction)

        # 获取拖放位置
        drop_position = event.position().toPoint()

        # 检查是否拖放到面包屑导航栏
        if self.nav_container.geometry().contains(drop_position):
            target_widget = self.nav_container.childAt(drop_position)
            if isinstance(target_widget, QPushButton):
                path = target_widget.property("path")
                if path:
                    # 解析路径
                    parts = [part for part in path.split("/") if part]
                    if len(parts) >= 1:
                        target_category = parts[0]
                        target_subfolder = "/".join(parts[1:]) if len(parts) > 1 else ""
                        # 处理内部文件拖放
                        if mime_data.hasFormat("application/x-file-info"):
                            success = self.drag_drop_manager.handle_internal_drop(
                                mime_data, target_category, target_subfolder
                            )
                            if success:
                                self.refresh_current_view()
                                self.statusbar_message.emit("文件移动成功")
                        event.acceptProposedAction()
                        return

        # 检查是否拖放到文件夹上
        target_folder = self._get_folder_at_position(drop_position)

        target_subfolder = self.current_subfolder
        if target_folder:
            # 如果拖放到文件夹上，更新目标子文件夹路径
            if self.current_subfolder:
                target_subfolder = f"{self.current_subfolder}/{target_folder}"
            else:
                target_subfolder = target_folder

        # 处理外部文件拖放
        if mime_data.hasUrls():
            imported_files = self.drag_drop_manager.handle_external_drop(
                mime_data, self.current_category, target_subfolder
            )
            if imported_files:
                self.refresh_current_view()
                self.statusbar_message.emit(f"成功导入 {len(imported_files)} 个文件")

        # 处理内部文件拖放
        elif mime_data.hasFormat("application/x-file-info"):
            success = self.drag_drop_manager.handle_internal_drop(
                mime_data, self.current_category, target_subfolder
            )
            if success:
                self.refresh_current_view()
                self.statusbar_message.emit("文件移动成功")

        event.acceptProposedAction()

    def _get_folder_at_position(self, position: QPoint) -> str:
        """获取指定位置的文件夹名称"""
        # 遍历所有文件组件，检查位置是否在文件夹上
        for widget in self.file_widgets:
            if widget.geometry().contains(position):
                file_info = widget.file_info
                if file_info.get('is_folder', False):
                    return file_info.get('name', '')
        return ""

    def on_files_dropped(self, file_paths: list, category: str):
        """文件拖放完成处理"""
        print(f"文件拖放完成: {len(file_paths)} 个文件到 {category}")

    def on_file_moved(self, file_info: dict, source_category: str, target_category: str):
        """文件移动完成处理"""
        print(f"文件移动: {file_info['name']} 从 {source_category} 到 {target_category}")
        # 刷新视图以显示移动后的状态
        self.refresh_current_view()
    
    def set_category(self, category: str, subfolder: str = ""):
        """设置当前分类和子文件夹"""
        self.current_category = category
        self.current_subfolder = subfolder

        if subfolder:
            self.current_path = f"/{category}/{subfolder}"
        else:
            self.current_path = f"/{category}"

        self.update_breadcrumb_navigation()
        self.refresh_current_view()
    
    def refresh_current_view(self):
        """刷新当前视图"""
        # 清除现有文件组件
        self.clear_file_widgets()

        if not self.current_category:
            return

        # 获取文件和文件夹列表
        items = []

        # 特殊处理回收站
        if self.current_category == "回收站":
            # 从文件系统管理器获取已删除的文件
            deleted_files = self.fs_manager.get_deleted_files()
            for file_data in deleted_files:
                file_info = {
                    'id': file_data['id'],
                    'name': file_data['name'],
                    'path': file_data['path'],
                    'category': "回收站",
                    'type': file_data['type'],
                    'is_folder': False,
                    'creation_time': file_data.get('creation_time'),
                    'modified_time': file_data.get('modified_time'),
                    'delete_time': file_data.get('delete_time'),
                    'original_category': file_data.get('category')  # 原始分类
                }
                items.append(file_info)
        else:
            # 构建实际的文件系统路径
            storage_path = self.config_manager.get_storage_path()
            current_dir = storage_path / self.current_category

            if self.current_subfolder:
                current_dir = current_dir / self.current_subfolder

            if current_dir.exists():
                # 添加文件夹
                for item in current_dir.iterdir():
                    if item.is_dir():
                        folder_info = {
                            'id': 0,  # 文件夹暂时不存储在数据库中
                            'name': item.name,
                            'path': str(item),
                            'category': self.current_category,
                            'type': 'folder',
                            'is_folder': True,
                            'creation_time': None,
                            'modified_time': None
                        }
                        items.append(folder_info)

                # 从文件系统管理器获取文件信息
                fs_files = self.fs_manager.get_files_by_category(self.current_category)

                # 创建路径到文件信息的映射
                fs_file_map = {}
                for fs_file in fs_files:
                    if not fs_file.get('is_deleted', False):  # 排除已删除的文件
                        fs_file_map[fs_file['path']] = fs_file

                # 添加文件
                for item in current_dir.iterdir():
                    if item.is_file() and not item.name.startswith('.'):  # 过滤隐藏文件
                        item_path = str(item)

                        # 从文件系统管理器获取文件信息，如果不存在则创建默认信息
                        if item_path in fs_file_map:
                            fs_file = fs_file_map[item_path]
                            file_info = {
                                'id': fs_file['id'],
                                'name': fs_file['name'],
                                'path': fs_file['path'],
                                'category': fs_file['category'],
                                'type': fs_file['type'],
                                'is_folder': False,
                                'creation_time': fs_file.get('creation_time'),
                                'modified_time': fs_file.get('modified_time'),
                                'size': fs_file.get('size', 0),
                                'color': fs_file.get('color')
                            }
                        else:
                            # 文件不在管理器中，立即添加到管理器
                            try:
                                file_type = item.suffix.lower().lstrip('.') or 'file'
                                new_id = self.fs_manager.add_file(
                                    name=item.name,
                                    path=item_path,
                                    category=self.current_category,
                                    file_type=file_type
                                )

                                file_info = {
                                    'id': new_id if new_id else self.fs_manager._get_file_id(item_path),
                                    'name': item.name,
                                    'path': item_path,
                                    'category': self.current_category,
                                    'type': file_type,
                                    'is_folder': False,
                                    'creation_time': None,
                                    'modified_time': None,
                                    'size': item.stat().st_size if item.exists() else 0,
                                    'color': None
                                }

                                print(f"自动添加文件到数据库: {item.name} (ID: {new_id})")

                            except Exception as e:
                                print(f"添加文件到数据库失败: {e}")
                                # 如果添加失败，仍然创建文件信息但ID为0
                                file_info = {
                                    'id': 0,
                                    'name': item.name,
                                    'path': item_path,
                                    'category': self.current_category,
                                    'type': item.suffix.lower().lstrip('.') or 'file',
                                    'is_folder': False,
                                    'creation_time': None,
                                    'modified_time': None,
                                    'size': item.stat().st_size if item.exists() else 0,
                                    'color': None
                                }

                        items.append(file_info)

        # 显示文件和文件夹
        self.display_items(items)
    
    def clear_file_widgets(self):
        """清除文件组件"""
        for widget in self.file_widgets:
            widget.deleteLater()
        self.file_widgets.clear()
        self.selected_files.clear()
    
    def display_items(self, items: list):
        """显示文件和文件夹"""
        self.current_items = items
        self.reflow_items()

    def reflow_items(self):
        """重新排列项目"""
        if not hasattr(self, 'current_items'):
            return

        # 清除现有布局
        for i in reversed(range(self.file_layout.count())):
            item = self.file_layout.itemAt(i)
            if item is not None:
                widget = item.widget()
                if widget is not None:
                    widget.setParent(None)
        
        self.file_widgets.clear()

        # 获取缩略图大小设置
        thumbnail_size = self.config_manager.get_setting("thumbnail_size", 100)
        item_width = thumbnail_size + 20  # 缩略图宽度 + 边距

        # 计算可容纳的列数，确保至少有1列
        available_width = self.scroll_area.width() - 30  # 减去滚动条和边距
        columns = max(1, available_width // item_width)

        for i, item in enumerate(self.current_items):
            row = i // columns
            col = i % columns

            file_widget = FileItemWidget(
                item,
                self.thumbnail_generator,
                self.drag_drop_manager,
                self.config_manager.settings["thumbnail_size"]
            )
            file_widget.clicked.connect(self.on_file_clicked)
            file_widget.double_clicked.connect(self.on_file_double_clicked)

            self.file_layout.addWidget(file_widget, row, col)
            self.file_widgets.append(file_widget)
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self.reflow_items()

    def on_file_clicked(self, file_info: dict):
        """文件点击事件 - 支持多选"""
        sender = self.sender()
        if not sender or not isinstance(sender, FileItemWidget):
            return

        # 获取键盘修饰键
        modifiers = QApplication.keyboardModifiers()

        if modifiers & Qt.KeyboardModifier.ControlModifier:
            # Ctrl+点击：切换选中状态
            if sender.selected:
                sender.set_selected(False)
                if file_info in self.selected_files:
                    self.selected_files.remove(file_info)
            else:
                sender.set_selected(True)
                if file_info not in self.selected_files:
                    self.selected_files.append(file_info)
        elif modifiers & Qt.KeyboardModifier.ShiftModifier:
            # Shift+点击：范围选择
            if self.selected_files:
                self._select_range(sender)
            else:
                # 如果没有之前的选择，就单选
                self._single_select(sender, file_info)
        else:
            # 普通点击：单选
            self._single_select(sender, file_info)

        # 发送选择信号
        if self.selected_files:
            self.file_selected.emit(self.selected_files[-1])

        # 更新状态栏信息
        self._update_selection_status()

    def _single_select(self, widget: FileItemWidget, file_info: dict):
        """单选文件"""
        # 清除其他选中状态
        for w in self.file_widgets:
            w.set_selected(False)

        # 设置当前选中
        widget.set_selected(True)
        self.selected_files = [file_info]

    def _select_range(self, end_widget: FileItemWidget):
        """范围选择"""
        if not self.selected_files:
            return

        # 找到最后一个选中的文件对应的widget
        last_selected_widget = None
        for widget in self.file_widgets:
            if widget.file_info in self.selected_files:
                last_selected_widget = widget

        if not last_selected_widget:
            return

        # 找到两个widget在布局中的位置
        start_index = self.file_widgets.index(last_selected_widget)
        end_index = self.file_widgets.index(end_widget)

        # 确保start_index <= end_index
        if start_index > end_index:
            start_index, end_index = end_index, start_index

        # 选择范围内的所有文件
        self.selected_files.clear()
        for i in range(start_index, end_index + 1):
            widget = self.file_widgets[i]
            widget.set_selected(True)
            self.selected_files.append(widget.file_info)
    
    def on_file_double_clicked(self, file_info: dict):
        """文件双击事件"""
        if file_info.get('is_folder', False):
            # 进入文件夹
            folder_name = file_info['name']
            if self.current_subfolder:
                new_subfolder = f"{self.current_subfolder}/{folder_name}"
            else:
                new_subfolder = folder_name

            self.set_category(self.current_category, new_subfolder)

            # 发送导航信号给主窗口
            self.folder_entered.emit(self.current_category, new_subfolder)
        else:
            # 如果是文件，显示预览对话框
            preview_dialog = PreviewDialog(file_info, self.config_manager, self)
            preview_dialog.exec()


    
    def search_files(self, query: str):
        """搜索文件"""
        if not query.strip():
            self.refresh_current_view()
            return

        # 清除现有显示
        self.clear_file_widgets()

        # 搜索文件
        files = self.fs_manager.search_files(query, self.current_category)
        self.display_items(files)

    def display_search_results(self, search_results: list):
        """显示搜索结果"""
        # 清除现有显示
        self.clear_file_widgets()

        # 更新路径显示为搜索结果
        self.clear_breadcrumb_navigation()
        search_label = QLabel(f"搜索结果: 找到 {len(search_results)} 个文件")
        search_label.setFont(QFont("Microsoft YaHei", 10))
        self.nav_layout.addWidget(search_label)

        # 显示搜索结果
        self.display_items(search_results)
    
    def import_files(self, file_paths: list):
        """导入文件"""
        if not self.current_category:
            QMessageBox.warning(self, "警告", "请先选择一个分类")
            return
        
        success_count = 0
        for file_path in file_paths:
            if self.file_operations.import_file(file_path, self.current_category):
                success_count += 1
        
        if success_count > 0:
            QMessageBox.information(self, "导入完成", f"成功导入 {success_count} 个文件")
            self.refresh_current_view()
        else:
            QMessageBox.warning(self, "导入失败", "没有文件被成功导入")
    
    def select_all(self):
        """全选"""
        for widget in self.file_widgets:
            widget.set_selected(True)
        self.selected_files = [widget.file_info for widget in self.file_widgets]
        self._update_selection_status()
    
    def rename_selected(self):
        """重命名选中项"""
        if not self.selected_files:
            QMessageBox.information(self, "提示", "请先选择要重命名的文件")
            return

        # 打开重命名对话框
        dialog = RenameDialog(self.selected_files, self.config_manager, self.fs_manager, self)
        dialog.files_renamed.connect(self.on_files_renamed)
        dialog.exec()

    def on_files_renamed(self, files: list):
        """文件重命名完成处理"""
        self.refresh_current_view()
        self.statusbar_message.emit(f"成功重命名 {len(files)} 个文件")
    
    def batch_rename(self):
        """批量重命名"""
        selected_files = self.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要重命名的文件")
            return

        # 打开批量重命名对话框
        dialog = BatchRenameDialog(
            self.config_manager, self.fs_manager,
            selected_files, self.current_category, self
        )
        dialog.files_renamed.connect(self.on_files_renamed)
        dialog.exec()
    

    
    def contextMenuEvent(self, event: QContextMenuEvent):
        """右键菜单事件"""
        menu = QMenu(self)
        selected_files = self.get_selected_files()

        # 根据当前分类调整菜单
        is_recycle_bin = (self.current_category == "回收站")

        if not is_recycle_bin:
            # 非回收站的右键菜单
            if selected_files:
                # 文件操作
                if len(selected_files) == 1:
                    rename_action = menu.addAction("🏷️ 重命名")
                    rename_action.triggered.connect(self.rename_selected)
                else:
                    batch_rename_action = menu.addAction("🏷️ 批量重命名")
                    batch_rename_action.triggered.connect(self.batch_rename)

                menu.addSeparator()

                # 复制粘贴
                copy_action = menu.addAction("📋 复制")
                copy_action.triggered.connect(self.copy_selected)

                if self.has_clipboard_content():
                    paste_action = menu.addAction("📋 粘贴")
                    paste_action.triggered.connect(self.paste_files)

                menu.addSeparator()

                # 导出
                export_action = menu.addAction("📤 导出")
                export_action.triggered.connect(self.export_selected)

                # 删除
                if SEND2TRASH_AVAILABLE:
                    delete_action = menu.addAction("🗑️ 移至回收站")
                else:
                    delete_action = menu.addAction("🗑️ 删除")
                delete_action.triggered.connect(self.delete_selected)



            else:
                # 没有选中文件时的菜单
                import_action = menu.addAction("📁 导入文件")
                import_action.triggered.connect(self.show_import_dialog)

                new_folder_action = menu.addAction("➕ 新建文件夹")
                new_folder_action.triggered.connect(self.create_new_folder)

                if self.has_clipboard_content():
                    menu.addSeparator()
                    paste_action = menu.addAction("📋 粘贴")
                    paste_action.triggered.connect(self.paste_files)

            menu.addSeparator()

            # 搜索
            search_action = menu.addAction("搜索")
            search_action.triggered.connect(self.show_search)

        else:
            # 回收站的右键菜单
            if selected_files:
                # 批量还原
                restore_action = menu.addAction("批量还原")
                restore_action.triggered.connect(self.restore_selected)

                menu.addSeparator()

                # 批量删除（永久删除）
                permanent_delete_action = menu.addAction("永久删除")
                permanent_delete_action.triggered.connect(self.permanent_delete_selected)

            menu.addSeparator()



            # 搜索
            search_action = menu.addAction("搜索")
            search_action.triggered.connect(self.show_search)

        menu.exec(event.globalPos())

    def create_new_folder(self):
        """创建新文件夹"""
        if not self.current_category or self.current_category == "回收站":
            QMessageBox.warning(self, "警告", "无法在此处创建文件夹。")
            return

        folder_name, ok = QInputDialog.getText(self, "新建文件夹", "请输入文件夹名称:")
        if ok and folder_name:
            try:
                self.file_operations.create_folder(folder_name, self.current_category, self.current_subfolder)
                self.refresh_current_view()
                self.statusbar_message.emit(f"成功创建文件夹: {folder_name}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"创建文件夹失败: {e}")
    
    def show_import_dialog(self):
        """显示导入对话框"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择要导入的文件", "", 
            "所有文件 (*);;图片文件 (*.jpg *.jpeg *.png *.gif *.bmp);;视频文件 (*.mp4 *.avi *.mov *.mkv)"
        )
        
        if file_paths:
            self.import_files(file_paths)

    def get_selected_files(self) -> list:
        """获取选中的文件列表"""
        return self.selected_files.copy()

    def clear_all_selections(self):
        """清除所有选择"""
        for widget in self.file_widgets:
            widget.set_selected(False)
        self.selected_files.clear()
        self._update_selection_status()

    def delete_selected(self):
        """删除选中的文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要删除的文件")
            return

        # 确认删除
        message = f"确定要将选中的 {len(selected_files)} 个文件移动到回收站吗？"

        reply = QMessageBox.question(
            self, "确认删除",
            message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            success_count = 0
            failed_files = []

            for file_info in selected_files:
                try:
                    file_path = file_info.get('path', '')
                    if not file_path or not os.path.exists(file_path):
                        failed_files.append(file_info['name'])
                        continue

                    # 检查文件ID
                    file_id = file_info.get('id', 0)
                    if file_id == 0:
                        # 文件不在数据库中，先添加到数据库
                        try:
                            file_id = self.fs_manager.add_file(
                                name=file_info['name'],
                                path=file_info['path'],
                                category=file_info['category'],
                                file_type=file_info['type']
                            )
                            if file_id == 0:
                                print(f"无法添加文件到数据库: {file_info.get('name', '未知文件')}")
                                failed_files.append(file_info['name'])
                                continue
                        except Exception as e:
                            print(f"添加文件到数据库失败: {e}")
                            failed_files.append(file_info['name'])
                            continue

                    # 统一使用程序内置回收站功能
                    # 这样可以确保文件在回收站中正确显示
                    try:
                        if self.file_operations.delete_file(file_id):
                            success_count += 1
                        else:
                            failed_files.append(file_info['name'])
                    except Exception as delete_error:
                        print(f"删除文件失败 {file_info['name']}: {delete_error}")
                        failed_files.append(file_info['name'])

                except Exception as e:
                    print(f"处理文件失败 {file_info['name']}: {e}")
                    failed_files.append(file_info['name'])

            # 显示结果
            if success_count > 0:
                self.statusbar_message.emit(f"成功将 {success_count} 个文件移动到回收站")

            if failed_files:
                QMessageBox.warning(
                    self, "删除失败",
                    f"以下文件删除失败：\n" + "\n".join(failed_files[:10]) +
                    (f"\n... 还有 {len(failed_files) - 10} 个文件" if len(failed_files) > 10 else "")
                )

            # 刷新视图
            self.refresh_current_view()

    def change_color(self):
        """更改选中文件的颜色"""
        selected_files = self.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要更改颜色的文件")
            return

        # 打开颜色选择对话框
        color_dialog = ColorDialog(
            self.config_manager, self.fs_manager,
            "file", selected_files, self.current_category, self
        )
        color_dialog.color_changed.connect(self.on_color_changed)
        color_dialog.exec()

    def on_color_changed(self, color: str):
        """颜色改变处理"""
        # 刷新视图以显示新颜色
        self.refresh_current_view()

    # 右键菜单相关方法
    def copy_selected(self):
        """复制选中的文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            return

        # 将文件信息保存到剪贴板
        import json
        clipboard_data = {
            'type': 'file_copy',
            'files': selected_files,
            'source_category': self.current_category
        }

        clipboard = QApplication.clipboard()
        clipboard.setText(json.dumps(clipboard_data))

        self.statusbar_message.emit(f"已复制 {len(selected_files)} 个文件")

    def has_clipboard_content(self) -> bool:
        """检查剪贴板是否有文件内容"""
        try:
            clipboard = QApplication.clipboard()
            text = clipboard.text()
            if text:
                import json
                data = json.loads(text)
                return data.get('type') == 'file_copy'
        except:
            pass
        return False

    def paste_files(self):
        """粘贴文件"""
        if not self.current_category or self.current_category == "回收站":
            QMessageBox.warning(self, "警告", "无法在当前位置粘贴文件")
            return

        try:
            clipboard = QApplication.clipboard()
            text = clipboard.text()
            if text:
                import json
                data = json.loads(text)
                if data.get('type') == 'file_copy':
                    files = data.get('files', [])
                    if files:
                        # 执行文件复制
                        success_count = 0
                        failed_files = []

                        for file_info in files:
                            try:
                                source_path = file_info.get('path')
                                if source_path and os.path.exists(source_path):
                                    # 使用文件操作类复制文件
                                    success, _, message = self.file_operations.import_file_with_conflict_check(
                                        source_path, self.current_category, self.current_subfolder
                                    )

                                    if success:
                                        success_count += 1
                                    else:
                                        failed_files.append(file_info.get('name', '未知文件'))
                                else:
                                    failed_files.append(file_info.get('name', '未知文件'))
                            except Exception as e:
                                failed_files.append(file_info.get('name', '未知文件'))
                                print(f"粘贴文件失败: {e}")

                        # 显示结果
                        if success_count > 0:
                            QMessageBox.information(self, "粘贴完成", f"成功粘贴 {success_count} 个文件")
                            self.refresh_current_view()

                        if failed_files:
                            QMessageBox.warning(self, "粘贴失败", f"以下文件粘贴失败:\n{', '.join(failed_files)}")
                    else:
                        QMessageBox.information(self, "提示", "剪贴板中没有文件")
                else:
                    QMessageBox.information(self, "提示", "剪贴板中没有有效的文件数据")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"粘贴失败: {str(e)}")

    def export_selected(self):
        """导出选中的文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要导出的文件")
            return

        from ui.export_dialog import ExportDialog
        export_dialog = ExportDialog(self.config_manager, self.fs_manager, selected_files, self)
        export_dialog.exec()

    def show_search(self):
        """显示搜索对话框"""
        from ui.search_dialog import SearchDialog
        from utils.search_sort import SearchSortManager

        # 创建搜索管理器
        search_manager = SearchSortManager(self.config_manager, self.fs_manager)

        # 创建搜索对话框
        search_dialog = SearchDialog(
            self.config_manager,
            self.fs_manager,
            search_manager,
            self
        )

        # 连接文件选择信号
        search_dialog.file_selected.connect(self.on_search_file_selected)

        # 显示对话框
        search_dialog.exec()

    def on_search_file_selected(self, file_info: dict):
        """搜索文件被选中"""
        # 可以在这里添加文件选中后的处理逻辑
        # 比如跳转到文件所在的分类和位置
        category = file_info.get('category', '')
        if category and category != self.current_category:
            # 切换到文件所在的分类
            self.category_changed.emit(category, "")

        # 发送状态消息
        self.statusbar_message.emit(f"已选择文件: {file_info.get('name', '')}")







    def change_sort_order(self, sort_type: str):
        """更改排序方式"""
        from utils.search_sort import SortBy

        sort_mapping = {
            "name": SortBy.NAME,
            "type": SortBy.TYPE,
            "time": SortBy.MODIFIED_TIME,
            "delete_time": SortBy.MODIFIED_TIME  # 回收站按删除时间排序
        }

        if sort_type in sort_mapping:
            # 这里应该调用搜索排序管理器
            self.statusbar_message.emit(f"排序方式已更改为: {sort_type}")
            self.refresh_current_view()

    def permanent_delete_selected(self):
        """永久删除选中的文件"""
        if self.current_category != "回收站":
            return

        selected_files = self.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要永久删除的文件")
            return

        reply = QMessageBox.question(
            self, "确认永久删除",
            f"确定要永久删除选中的 {len(selected_files)} 个项目吗？此操作不可恢复。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            success_count = 0
            for item in selected_files:
                if item.get('is_folder', False):
                    # 删除文件夹
                    if self.file_operations.permanently_delete_folder(item['path']):
                        success_count += 1
                else:
                    # 永久删除文件
                    try:
                        # 检查文件是否存在
                        if os.path.exists(item['path']):
                            # 从文件系统删除
                            os.remove(item['path'])
                        # 清理元数据（无论文件是否存在都要清理）
                        self.fs_manager.cleanup_non_existent_files()
                        success_count += 1
                    except OSError as e:
                        # 如果文件不存在，也算成功
                        if e.errno == 2:  # 文件不存在
                            self.fs_manager.cleanup_non_existent_files()
                            success_count += 1
                        else:
                            print(f"删除文件失败: {e}")
            
            if success_count > 0:
                self.statusbar_message.emit(f"成功永久删除了 {success_count} 个项目")
                self.refresh_current_view()
    
    def restore_selected(self):
        """还原选中的文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要还原的文件")
            return

        # 确认还原
        reply = QMessageBox.question(
            self, "确认还原",
            f"确定要还原选中的 {len(selected_files)} 个文件吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 执行还原操作
            success_count = 0
            failed_files = []

            for file_info in selected_files:
                try:
                    if self.file_operations.restore_file(file_info['id']):
                        success_count += 1
                        print(f"还原文件成功: {file_info['name']}")
                    else:
                        failed_files.append(file_info['name'])
                        print(f"还原文件失败: {file_info['name']}")
                except Exception as e:
                    failed_files.append(file_info['name'])
                    print(f"还原文件异常: {file_info['name']}, 错误: {e}")

            # 显示结果
            if success_count > 0:
                QMessageBox.information(self, "还原完成", f"成功还原 {success_count} 个文件")

            if failed_files:
                QMessageBox.warning(self, "还原失败", f"以下文件还原失败:\n{', '.join(failed_files)}")

            # 刷新视图
            self.refresh_current_view()

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 开始框选"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否点击在空白区域或滚动区域
            click_pos = event.position().toPoint()
            widget_at_pos = self.childAt(click_pos)

            # 检查是否点击在文件项上
            clicked_on_file = False
            for file_widget in self.file_widgets:
                if file_widget.geometry().contains(click_pos):
                    clicked_on_file = True
                    break

            # 只有在空白区域点击时才开始框选
            if not clicked_on_file:
                # 开始框选
                self.selection_start_point = click_pos
                self.is_selecting = True

                # 如果没有按住Ctrl键，清除之前的选择
                if not (QApplication.keyboardModifiers() & Qt.KeyboardModifier.ControlModifier):
                    self.clear_all_selections()

                # 创建橡皮筋选择框
                if self.rubber_band:
                    self.rubber_band.deleteLater()
                self.rubber_band = QRubberBand(QRubberBand.Shape.Rectangle, self)
                self.rubber_band.setGeometry(QRect(self.selection_start_point, self.selection_start_point))
                self.rubber_band.show()

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 更新框选区域"""
        if self.is_selecting and self.rubber_band:
            # 更新橡皮筋选择框的大小
            current_pos = event.position().toPoint()
            selection_rect = QRect(self.selection_start_point, current_pos).normalized()
            self.rubber_band.setGeometry(selection_rect)

            # 实时更新选择的文件
            self._update_selection_by_rect(selection_rect)

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件 - 完成框选"""
        if event.button() == Qt.MouseButton.LeftButton and self.is_selecting:
            self.is_selecting = False

            if self.rubber_band:
                # 最终确定选择区域
                selection_rect = self.rubber_band.geometry()
                self._update_selection_by_rect(selection_rect)

                # 隐藏并删除橡皮筋
                self.rubber_band.hide()
                self.rubber_band.deleteLater()
                self.rubber_band = None

        super().mouseReleaseEvent(event)

    def update_breadcrumb_navigation(self):
        """更新面包屑导航"""
        self.clear_breadcrumb_navigation()

        # 解析路径
        path_parts = []
        if self.current_path.startswith("/"):
            path_parts = [part for part in self.current_path.split("/") if part]

        # 添加根目录按钮
        if path_parts:
            root_btn = QPushButton(path_parts[0])
            root_btn.setFlat(True)
            root_btn.setProperty("path", f"/{path_parts[0]}")
            root_btn.setStyleSheet("""
                QPushButton {
                    border: none;
                    padding: 2px 8px;
                    color: #0078d4;
                    text-decoration: underline;
                }
                QPushButton:hover {
                    background-color: #e5f3ff;
                }
            """)
            root_btn.clicked.connect(lambda: self.navigate_to_path(f"/{path_parts[0]}"))
            self.nav_layout.addWidget(root_btn)

            # 添加子路径
            for i, part in enumerate(path_parts[1:], 1):
                # 添加分隔符
                separator = QLabel(" > ")
                separator.setStyleSheet("color: #666666;")
                self.nav_layout.addWidget(separator)

                # 添加路径按钮
                path_btn = QPushButton(part)
                path_btn.setFlat(True)
                current_path = "/" + "/".join(path_parts[:i+1])
                path_btn.setProperty("path", current_path)
                path_btn.setStyleSheet("""
                    QPushButton {
                        border: none;
                        padding: 2px 8px;
                        color: #0078d4;
                        text-decoration: underline;
                    }
                    QPushButton:hover {
                        background-color: #e5f3ff;
                    }
                """)
                path_btn.clicked.connect(lambda checked, path=current_path: self.navigate_to_path(path))
                self.nav_layout.addWidget(path_btn)
        else:
            # 根目录
            root_label = QLabel("根目录")
            root_label.setFont(QFont("Microsoft YaHei", 10))
            self.nav_layout.addWidget(root_label)

    def clear_breadcrumb_navigation(self):
        """清除面包屑导航"""
        while self.nav_layout.count():
            child = self.nav_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def navigate_to_path(self, path: str):
        """导航到指定路径"""
        if path.startswith("/"):
            parts = [part for part in path.split("/") if part]
            if len(parts) >= 1:
                category = parts[0]
                subfolder = "/".join(parts[1:]) if len(parts) > 1 else ""
                self.set_category(category, subfolder)

    def _update_selection_by_rect(self, rect: QRect):
        """根据矩形区域更新文件选择"""
        # 如果按住Ctrl键，则添加到现有选择；否则替换选择
        ctrl_pressed = QApplication.keyboardModifiers() & Qt.KeyboardModifier.ControlModifier

        if not ctrl_pressed:
            # 清除之前的选择
            for widget in self.file_widgets:
                widget.set_selected(False)
            self.selected_files.clear()

        # 检查每个文件widget是否与选择矩形相交
        for widget in self.file_widgets:
            if not widget.isVisible():
                continue

            # 获取widget在当前视图中的位置
            widget_rect = widget.geometry()

            # 检查选择矩形是否与widget相交
            if rect.intersects(widget_rect):
                if not widget.selected:
                    widget.set_selected(True)
                    if widget.file_info not in self.selected_files:
                        self.selected_files.append(widget.file_info)
            elif not ctrl_pressed:
                # 如果不是Ctrl+框选，取消选择不在框内的文件
                if widget.selected:
                    widget.set_selected(False)
                    if widget.file_info in self.selected_files:
                        self.selected_files.remove(widget.file_info)

    def _update_selection_status(self):
        """更新选择状态信息"""
        count = len(self.selected_files)
        if count == 0:
            self.statusbar_message.emit("未选择文件")
        elif count == 1:
            file_info = self.selected_files[0]
            file_name = file_info.get('name', '未知文件')
            file_size = self._format_file_size(file_info.get('size', 0))
            file_type = file_info.get('type', '未知').upper()
            self.statusbar_message.emit(f"已选择: {file_name} ({file_type}, {file_size})")
        else:
            total_size = sum(file.get('size', 0) for file in self.selected_files)
            formatted_size = self._format_file_size(total_size)
            self.statusbar_message.emit(f"已选择 {count} 个文件 (总大小: {formatted_size})")

    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)

        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1

        if i == 0:
            return f"{int(size)} {size_names[i]}"
        else:
            return f"{size:.1f} {size_names[i]}"

    def select_file(self, file_to_select: dict):
        """选中指定文件"""
        for widget in self.file_widgets:
            if widget.file_info['id'] == file_to_select['id']:
                self._single_select(widget, widget.file_info)
                self.scroll_area.ensureWidgetVisible(widget)
                break
